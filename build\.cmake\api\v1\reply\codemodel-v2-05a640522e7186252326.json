{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "NodeNetwork", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "NodeNetwork::@6890427a1f51a3e7e1df", "jsonFile": "target-NodeNetwork-Debug-ec7a3b5887b88c47e695.json", "name": "NodeNetwork", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "F:/Projects/VSCode/NodeNetwork/build", "source": "F:/Projects/VSCode/NodeNetwork"}, "version": {"major": 2, "minor": 7}}