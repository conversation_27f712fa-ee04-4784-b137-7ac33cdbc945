{"cmake": {"generator": {"multiConfig": false, "name": "MinGW Makefiles"}, "paths": {"cmake": "E:/cmake/bin/cmake.exe", "cpack": "E:/cmake/bin/cpack.exe", "ctest": "E:/cmake/bin/ctest.exe", "root": "E:/cmake/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 3, "string": "3.30.3", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-05a640522e7186252326.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-3b9b095f9a886d6a25fe.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-2fe1abb47cc378750777.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-b89b3de09f892a592718.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-3b9b095f9a886d6a25fe.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-05a640522e7186252326.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "toolchains-v1-b89b3de09f892a592718.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-2fe1abb47cc378750777.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}