[{"directory": "F:/Projects/VSCode/NodeNetwork/build", "command": "D:\\msys64\\mingw64\\bin\\g++.exe   -g -std=gnu++17 -o CMakeFiles\\NodeNetwork.dir\\NodeNetwork.cpp.obj -c F:\\Projects\\VSCode\\NodeNetwork\\NodeNetwork.cpp", "file": "F:/Projects/VSCode/NodeNetwork/NodeNetwork.cpp", "output": "CMakeFiles/NodeNetwork.dir/NodeNetwork.cpp.obj"}, {"directory": "F:/Projects/VSCode/NodeNetwork/build", "command": "D:\\msys64\\mingw64\\bin\\g++.exe   -g -std=gnu++17 -o CMakeFiles\\NodeNetwork.dir\\main.cpp.obj -c F:\\Projects\\VSCode\\NodeNetwork\\main.cpp", "file": "F:/Projects/VSCode/NodeNetwork/main.cpp", "output": "CMakeFiles/NodeNetwork.dir/main.cpp.obj"}]