#ifndef NODENETWORK_H
#define NODENETWORK_H

#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <string>

/**
 * @brief 节点网络类，用于管理节点、变量及其连接关系
 *
 * NodeNetwork 类提供了完整的节点网络管理功能，包括节点和变量的添加、
 * 连接关系的建立、路径搜索、终端节点识别等功能。支持变量激活状态管理，
 * 可以根据不同的搜索策略（物理邻接或仅活跃变量）进行网络遍历。
 */
class NodeNetwork
{
public:
    // 公共类型别名定义
    using StringList = std::vector<std::string>; ///< 字符串列表类型
    using PathList = std::vector<StringList>; ///< 路径列表类型（路径的集合）
    using NodeVariablesMap = std::unordered_map<std::string, StringList>; ///< 节点到变量列表的映射类型

    /**
     * @brief 默认构造函数
     *
     * 创建一个空的节点网络实例，初始化所有内部数据结构。
     */
    explicit NodeNetwork();

    /**
     * @brief 为指定节点添加多个变量
     *
     * 将一组变量关联到指定节点，并自动更新变量到节点的反向映射关系。
     * 如果节点已存在，将覆盖其原有的变量列表。
     *
     * @param node 节点名称
     * @param variables 要添加到节点的变量列表（StringList类型，即std::vector<std::string>）
     *
     * @note 此方法会自动更新公共变量信息，建立节点间的连接关系
     * @example
     * @code
     * NodeNetwork network;
     * network.addNodeVariables("Node1", {"var1", "var2", "var3"});
     * @endcode
     */
    void addNodeVariables(const std::string& node,
                          const StringList& variables);

    /**
     * @brief 获取指定节点的所有变量
     *
     * @param node 节点名称
     * @return StringList 节点包含的变量列表，如果节点不存在则返回空列表
     */
    StringList getNodeVariables(const std::string& node) const;

    /**
     * @brief 获取网络中所有节点的名称
     *
     * @return StringList 包含所有节点名称的列表
     */
    StringList getNodes() const;

    /**
     * @brief 为指定节点添加单个变量
     *
     * 向已存在的节点添加一个新变量，如果节点不存在则创建新节点。
     *
     * @param node 节点名称
     * @param variable 要添加的变量名称
     */
    void addNodeVariable(const std::string& node,
                         const std::string& variable);

    /**
     * @brief 设置变量的激活状态
     *
     * 控制变量是否处于激活状态。激活状态影响某些搜索算法的行为，
     * 如getAllActiveNodes方法只会通过激活的变量进行节点遍历。
     *
     * @param variable 变量名称
     * @param state 激活状态，true表示激活，false表示非激活
     *
     * @note 变量的激活状态不影响物理邻接关系的计算
     */
    void setVariableState(const std::string& variable,
                          bool state);
    /**
     * @brief 获取从指定节点开始的所有最终连接路径
     *
     * 使用深度优先搜索算法，从起始节点开始遍历网络，找到所有可能的最终路径。
     * 路径搜索考虑所有物理邻接关系，不受变量激活状态限制。
     *
     * @param startNode 起始节点名称
     * @param maxPathLength 最大路径长度限制
     * @return PathList 包含所有最终路径的列表，每个路径是一个节点名称序列
     *
     * @note 路径搜索基于物理邻接关系，即使变量未激活也会被考虑
     */
    PathList getFinalConnectedPaths(const std::string& startNode,
                                    int maxPathLength);

    /**
     * @brief 获取连通图中的所有终端节点
     *
     * 从指定起始节点开始，找到整个连通图中的所有终端节点（邻居数 ≤ 1的节点）。
     * 无论从哪个节点开始，都会返回相同的终端节点集合。
     *
     * @param startNode 起始节点名称
     * @param maxPathLength 搜索的最大路径长度限制
     * @return StringList 连通图中所有终端节点的列表
     *
     * @note 终端节点定义为邻居数量小于等于1的节点
     * @note 搜索基于物理邻接关系，不受变量激活状态影响
     */
    StringList getTerminalNodes(const std::string& startNode,
                                int maxPathLength);

    /**
     * @brief 获取终端节点及其私有变量
     *
     * 找到所有终端节点，并返回每个终端节点的私有变量（不与邻居节点共享的变量）。
     *
     * @param startNode 起始节点名称
     * @param maxPathLength 搜索的最大路径长度限制
     * @return NodeVariablesMap 终端节点到其私有变量列表的映射
     *
     * @note 私有变量是指该节点独有的、不与任何邻居节点共享的变量
     */
    NodeVariablesMap getTerminalNodesWithPrivateVariables(const std::string& startNode,
                                                          int maxPathLength);

    /**
     * @brief 获取连通图中的所有节点
     *
     * 从指定起始节点开始，找到整个连通图中的所有可达节点。
     * 搜索基于物理邻接关系，不受变量激活状态限制。
     *
     * @param startNode 起始节点名称
     * @param maxPathLength 搜索的最大路径长度限制，默认值为1000
     * @return StringList 连通图中所有节点的列表（按字母顺序排序）
     *
     * @note 无论从连通图中的哪个节点开始，都会返回相同的节点集合
     */
    StringList getAllNodes(const std::string& startNode,
                           int maxPathLength = 1000);

    /**
     * @brief 获取通过激活变量可达的所有节点
     *
     * 从指定起始节点开始，只通过激活状态的变量进行遍历，找到所有可达的节点。
     * 与getAllNodes不同，此方法只考虑激活的变量连接。
     *
     * @param startNode 起始节点名称
     * @param maxPathLength 搜索的最大路径长度限制，默认值为1000
     * @return StringList 通过激活变量可达的节点列表（按字母顺序排序）
     *
     * @note 只有激活状态的变量才会被用于建立节点间的连接
     * @note 如果没有激活的变量，通常只能到达起始节点本身
     */
    StringList getAllActiveNodes(const std::string& startNode,
                                 int maxPathLength = 1000);

    /**
     * @brief 获取指定节点的私有变量
     *
     * 返回指定节点的所有私有变量，即不与任何邻居节点共享的变量。
     *
     * @param node 节点名称
     * @return StringList 节点的私有变量列表（按字母顺序排序）
     *
     * @note 私有变量是该节点独有的，不会出现在任何邻居节点中
     */
    StringList getPrivateVariables(const std::string& node);

    /**
     * @brief 获取两个节点之间的公共变量
     *
     * 返回两个指定节点共同拥有的变量列表。
     *
     * @param node1 第一个节点名称
     * @param node2 第二个节点名称
     * @return StringList 两个节点的公共变量列表，如果没有公共变量或节点不存在则返回空列表
     *
     * @note 公共变量是同时存在于两个节点中的变量，这些变量建立了节点间的连接关系
     */
    StringList getCommonVariables(const std::string& node1,
                                  const std::string& node2);

    /**
     * @brief 获取指定节点与所有邻居节点的公共变量
     *
     * 返回指定节点与其每个邻居节点之间的公共变量映射。
     *
     * @param node 节点名称
     * @return NodeVariablesMap 邻居节点到公共变量列表的映射，如果节点不存在则返回空映射
     *
     * @example
     * @code
     * auto commonVars = network.getCommonVariables("Node1");
     * // commonVars["Node2"] 包含Node1和Node2之间的公共变量
     * @endcode
     */
    NodeVariablesMap getCommonVariables(const std::string& node);

    /**
     * @brief 获取指定节点的所有邻居节点
     *
     * 返回与指定节点有直接连接关系的所有邻居节点。连接关系通过共享变量建立。
     *
     * @param node 节点名称
     * @return StringList 邻居节点列表，如果节点不存在则返回空列表
     *
     * @note 邻居关系基于共享变量，即两个节点拥有至少一个相同的变量
     */
    StringList getNeighbors(const std::string& node) const;

    /**
     * @brief 获取通过指定变量连接的邻居节点
     *
     * 返回与指定节点通过特定变量建立连接的所有邻居节点。
     *
     * @param node 节点名称
     * @param variable 变量名称
     * @return StringList 通过指定变量连接的邻居节点列表
     *
     * @note 只返回与指定节点共享指定变量的其他节点
     */
    StringList getNeighbors(const std::string& node,
                            const std::string& variable) const;

    /**
     * @brief 获取包含指定变量的所有节点
     *
     * 返回所有包含指定变量的节点列表。
     *
     * @param variable 变量名称
     * @return StringList 包含该变量的所有节点列表，如果变量不存在则返回空列表
     *
     * @note 这些节点通过共享该变量形成连接关系
     */
    StringList getVariableNodes(const std::string& variable) const;

private:
    using StringSet = std::unordered_set<std::string>;
    using VariableNodesMap = std::unordered_map<std::string, StringSet>;
    using AllCommonVariablesMap = std::unordered_map<std::string, NodeVariablesMap>;

    void dfs(const std::string& currentNode,
             StringSet& visited,
             StringList& currentPath,
             PathList& finalPaths,
             size_t maxPathLength);
    void dfsCollectReachableNodes(const std::string& currentNode,
                                  StringSet& visited,
                                  StringSet& reachableNodes,
                                  size_t maxPathLength,
                                  bool onlyActiveVariables = false);
    void updateCommonVariables(const std::string& node,
                               const std::string& var);

    NodeVariablesMap nodes_; // 节点到变量点的映射
    VariableNodesMap variableToNodes_; // 变量点到节点的映射
    StringSet activeVariables_; // 激活的变量点集合
    AllCommonVariablesMap commonVariables_; // 公共变量点信息
};

#endif // NODENETWORK_H
