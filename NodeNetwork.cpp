#include "NodeNetwork.h"
#include <algorithm>

NodeNetwork::NodeNetwork()
{
}

void NodeNetwork::addNodeVariables(const std::string& node,
                                   const StringList& variables)
{
    nodes_[node] = variables;
    for (const auto& var : variables)
    {
        variableToNodes_[var].insert(node);
        // 更新公共变量点信息
        updateCommonVariables(node, var);
    }
}

NodeNetwork::StringList NodeNetwork::getNodeVariables(const std::string& node) const
{
    if (nodes_.find(node) == nodes_.end())
        return {};
    return nodes_.at(node);
}

NodeNetwork::StringList NodeNetwork::getNodes() const
{
    StringList result;
    for (const auto& [node, _] : nodes_)
    {
        result.push_back(node);
    }
    return result;
}

void NodeNetwork::addNodeVariable(const std::string& node,
                                  const std::string& variable)
{
    nodes_[node].push_back(variable);
    variableToNodes_[variable].insert(node);
    // 更新公共变量点信息
    updateCommonVariables(node, variable);
}

void NodeNetwork::setVariableState(const std::string& variable,
                                   bool state)
{
    if (state)
        activeVariables_.insert(variable);
    else
        activeVariables_.erase(variable);
}

NodeNetwork::PathList NodeNetwork::getFinalConnectedPaths(const std::string& startNode,
                                                         int maxPathLength)
{
    StringSet visited;
    StringList currentPath;
    PathList finalPaths;
    dfs(startNode, visited, currentPath, finalPaths, maxPathLength);
    return finalPaths;
}

NodeNetwork::StringList NodeNetwork::getTerminalNodes(const std::string& startNode,
                                                       int maxPathLength)
{
    // 首先找到从起始节点可达的所有节点
    StringSet reachableNodes;
    StringSet visited;
    dfsCollectReachableNodes(startNode, visited, reachableNodes, maxPathLength);

    // 然后在所有可达节点中找出终端节点（邻居数 <= 1）
    StringList endpoints;
    for (const auto& node : reachableNodes) {
        // 使用 getNeighbors 获取该节点的所有邻居
        StringList neighbors = getNeighbors(node);

        // 如果邻居数 <= 1，则为终端节点
        if (neighbors.size() <= 1) {
            endpoints.push_back(node);
        }
    }

    return endpoints;
}

NodeNetwork::StringList NodeNetwork::getPrivateVariables(const std::string& node)
{
    StringList nonSharedVariables;

    if (nodes_.find(node) != nodes_.end()) {
        for (const auto& var : nodes_.at(node)) {
            // 检查这个变量是否与邻居节点共享
            bool isShared = false;
            if (variableToNodes_.find(var) != variableToNodes_.end()) {
                for (const auto& neighbor : variableToNodes_.at(var)) {
                    if (neighbor != node) {
                        isShared = true;
                        break;
                    }
                }
            }

            // 如果变量不与任何邻居共享，则为非公共变量
            if (!isShared) {
                nonSharedVariables.push_back(var);
            }
        }
    }

    // 排序非公共变量以便输出一致
    std::sort(nonSharedVariables.begin(), nonSharedVariables.end());
    return nonSharedVariables;
}

NodeNetwork::NodeVariablesMap NodeNetwork::getTerminalNodesWithPrivateVariables(const std::string& startNode,
                                                                                         int maxPathLength)
{
    // 首先获取所有终端节点
    StringList terminalNodes = getTerminalNodes(startNode, maxPathLength);

    // 然后为每个终端节点获取其非公共变量
    NodeVariablesMap result;
    for (const auto& node : terminalNodes) {
        result[node] = getPrivateVariables(node);
    }

    return result;
}

NodeNetwork::StringList NodeNetwork::getAllNodes(const std::string& startNode,
                                                   int maxPathLength)
{
    // 找到从起始节点可达的所有节点
    StringSet reachableNodes;
    StringSet visited;
    dfsCollectReachableNodes(startNode, visited, reachableNodes, maxPathLength);

    // 将结果转换为vector并排序，以便输出一致
    StringList result(reachableNodes.begin(), reachableNodes.end());
    std::sort(result.begin(), result.end());

    return result;
}

NodeNetwork::StringList NodeNetwork::getAllActiveNodes(const std::string& startNode,
                                                        int maxPathLength)
{
    StringSet reachableNodes;
    StringSet visited;
    dfsCollectReachableNodes(startNode, visited, reachableNodes, maxPathLength, true);

    StringList result(reachableNodes.begin(), reachableNodes.end());
    std::sort(result.begin(), result.end());

    return result;
}

NodeNetwork::StringList NodeNetwork::getCommonVariables(const std::string& node1,
                                                        const std::string& node2)
{
    if (commonVariables_.find(node1) == commonVariables_.end() || commonVariables_[node1].find(node2) ==
        commonVariables_[node1].end())
        return {}; // 如果没有公共变量点，返回空列表
    return commonVariables_[node1][node2];
}

NodeNetwork::NodeVariablesMap NodeNetwork::getCommonVariables(const std::string& node)
{
    NodeVariablesMap result;
    if (commonVariables_.find(node) == commonVariables_.end())
        return result; // 如果节点不存在，返回空映射

    for (const auto& [otherNode, commonVars] : commonVariables_[node])
    {
        result[otherNode] = commonVars;
    }

    return result;
}

NodeNetwork::StringList NodeNetwork::getNeighbors(const std::string& node) const
{
    // 如果该节点不存在，返回空列表
    if (nodes_.find(node) == nodes_.end())
        return {};
    // 获取该节点的所有邻居节点
    StringList neighbors;
    for (const auto& var : nodes_.at(node))
    {
        for (const auto& neighbor : variableToNodes_.at(var))
        {
            if (neighbor != node)
            {
                if (std::find(neighbors.begin(), neighbors.end(), neighbor) == neighbors.end())
                    neighbors.push_back(neighbor);
            }
        }
    }
    return neighbors;
}

NodeNetwork::StringList NodeNetwork::getNeighbors(const std::string& node,
                                                   const std::string& variable) const
{
    // 如果该节点不存在，返回空列表
    if (nodes_.find(node) == nodes_.end())
        return {};
    // 获取该节点的所有邻居节点
    StringList neighbors;
    for (const auto& neighbor : variableToNodes_.at(variable))
    {
        if (neighbor != node)
            neighbors.push_back(neighbor);
    }
    return neighbors;
}

NodeNetwork::StringList NodeNetwork::getVariableNodes(const std::string& variable) const
{
    if (variableToNodes_.find(variable) == variableToNodes_.end())
        return {};
    StringList nodes;
    for (const auto& node : variableToNodes_.at(variable))
    {
        nodes.push_back(node);
    }
    return nodes;
}

void NodeNetwork::dfs(const std::string& currentNode,
                      StringSet& visited,
                      StringList& currentPath,
                      PathList& finalPaths,
                      size_t maxPathLength)
{
    visited.insert(currentNode);
    currentPath.push_back(currentNode);

    // 检查是否有未访问的邻居节点（不限于活跃变量）
    bool hasUnvisitedNeighbors = false;
    for (const auto& var : nodes_[currentNode])
    {
        for (const auto& neighbor : variableToNodes_[var])
        {
            if (neighbor != currentNode && visited.find(neighbor) == visited.end())
            {
                hasUnvisitedNeighbors = true;
                break;
            }
        }
        if (hasUnvisitedNeighbors)
            break;
    }

    // 如果没有未访问的邻居节点，或者路径长度达到限制，说明当前路径是一个最终路径
    if (!hasUnvisitedNeighbors || currentPath.size() >= maxPathLength)
    {
        finalPaths.push_back(currentPath);
    }
    else
    {
        for (const auto& var : nodes_[currentNode])
        {
            for (const auto& neighbor : variableToNodes_[var])
            {
                if (neighbor != currentNode && visited.find(neighbor) == visited.end())
                {
                    dfs(neighbor, visited, currentPath, finalPaths, maxPathLength);
                }
            }
        }
    }

    // 回溯前移除当前节点和标记
    currentPath.pop_back();
    visited.erase(currentNode);
}

void NodeNetwork::dfsCollectReachableNodes(const std::string& currentNode,
                                           StringSet& visited,
                                           StringSet& reachableNodes,
                                           size_t maxPathLength,
                                           bool onlyActiveVariables)
{
    visited.insert(currentNode);
    reachableNodes.insert(currentNode);

    // 如果已达到最大路径长度，停止搜索
    if (visited.size() >= maxPathLength) {
        visited.erase(currentNode);
        return;
    }

    // 遍历变量和邻居节点
    if (nodes_.find(currentNode) != nodes_.end()) {
        for (const auto& var : nodes_.at(currentNode)) {
            // 根据参数决定是否只考虑活跃变量
            bool shouldProcessVariable = true;
            if (onlyActiveVariables) {
                shouldProcessVariable = (activeVariables_.find(var) != activeVariables_.end());
            }

            if (variableToNodes_.find(var) != variableToNodes_.end() && shouldProcessVariable) {
                for (const auto& neighbor : variableToNodes_.at(var)) {
                    if (neighbor != currentNode && visited.find(neighbor) == visited.end()) {
                        dfsCollectReachableNodes(neighbor, visited, reachableNodes, maxPathLength, onlyActiveVariables);
                    }
                }
            }
        }
    }

    visited.erase(currentNode);
}

void NodeNetwork::updateCommonVariables(const std::string& node,
                                        const std::string& var)
{
    for (const auto& otherNode : variableToNodes_[var])
    {
        if (otherNode == node)
            continue; // 跳过自身

        // 更新 node 和 otherNode 之间的公共变量点
        commonVariables_[node][otherNode].push_back(var);
        commonVariables_[otherNode][node].push_back(var);
    }
}
