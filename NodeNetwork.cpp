#include "NodeNetwork.h"
#include <algorithm>

NodeNetwork::NodeNetwork()
{
}

void NodeNetwork::addNodeVariables(const std::string& node,
                                   const std::vector<std::string>& variables)
{
    nodes_[node] = variables;
    for (const auto& var : variables)
    {
        variableToNodes_[var].insert(node);
        // 更新公共变量点信息
        updateCommonVariables(node, var);
    }
}

std::vector<std::string> NodeNetwork::getNodeVariables(const std::string& node) const
{
    if (nodes_.find(node) == nodes_.end())
        return {};
    return nodes_.at(node);
}

std::vector<std::string> NodeNetwork::getNodes() const
{
    std::vector<std::string> result;
    for (const auto& [node, _] : nodes_)
    {
        result.push_back(node);
    }
    return result;
}

void NodeNetwork::addNodeVariable(const std::string& node,
                                  const std::string& variable)
{
    nodes_[node].push_back(variable);
    variableToNodes_[variable].insert(node);
    // 更新公共变量点信息
    updateCommonVariables(node, variable);
}

void NodeNetwork::setVariableState(const std::string& variable,
                                   bool state)
{
    if (state)
        activeVariables_.insert(variable);
    else
        activeVariables_.erase(variable);
}

std::vector<std::vector<std::string>> NodeNetwork::getFinalConnectedPaths(const std::string& startNode,
                                                                    int maxPathLength)
{
    std::unordered_set<std::string> visited;
    std::vector<std::string> currentPath;
    std::vector<std::vector<std::string>> finalPaths;
    dfs(startNode, visited, currentPath, finalPaths, maxPathLength);
    return finalPaths;
}

std::vector<std::string> NodeNetwork::getTerminalNodes(const std::string& startNode,
                                                int maxPathLength)
{
    // 首先找到从起始节点可达的所有节点
    std::unordered_set<std::string> reachableNodes;
    std::unordered_set<std::string> visited;
    dfsCollectReachableNodes(startNode, visited, reachableNodes, maxPathLength);

    // 然后在所有可达节点中找出终端节点（邻居数 <= 1）
    std::vector<std::string> endpoints;
    for (const auto& node : reachableNodes) {
        // 计算该节点的所有邻居数量（不限于活跃变量）
        std::unordered_set<std::string> uniqueNeighbors;
        if (nodes_.find(node) != nodes_.end()) {
            for (const auto& var : nodes_.at(node)) {
                if (variableToNodes_.find(var) != variableToNodes_.end()) {
                    for (const auto& neighbor : variableToNodes_.at(var)) {
                        if (neighbor != node) {
                            uniqueNeighbors.insert(neighbor);
                        }
                    }
                }
            }
        }

        // 如果邻居数 <= 1，则为终端节点
        if (uniqueNeighbors.size() <= 1) {
            endpoints.push_back(node);
        }
    }

    return endpoints;
}

std::vector<std::string> NodeNetwork::getNonSharedVariables(const std::string& node)
{
    std::vector<std::string> nonSharedVariables;

    if (nodes_.find(node) != nodes_.end()) {
        for (const auto& var : nodes_.at(node)) {
            // 检查这个变量是否与邻居节点共享
            bool isShared = false;
            if (variableToNodes_.find(var) != variableToNodes_.end()) {
                for (const auto& neighbor : variableToNodes_.at(var)) {
                    if (neighbor != node) {
                        isShared = true;
                        break;
                    }
                }
            }

            // 如果变量不与任何邻居共享，则为非公共变量
            if (!isShared) {
                nonSharedVariables.push_back(var);
            }
        }
    }

    // 排序非公共变量以便输出一致
    std::sort(nonSharedVariables.begin(), nonSharedVariables.end());
    return nonSharedVariables;
}

std::unordered_map<std::string, std::vector<std::string>> NodeNetwork::getTerminalNodesWithPrivateVariables(const std::string& startNode,
                                                                                                              int maxPathLength)
{
    // 首先获取所有终端节点
    std::vector<std::string> terminalNodes = getTerminalNodes(startNode, maxPathLength);

    // 然后为每个终端节点获取其非公共变量
    std::unordered_map<std::string, std::vector<std::string>> result;
    for (const auto& node : terminalNodes) {
        result[node] = getNonSharedVariables(node);
    }

    return result;
}

std::vector<std::string> NodeNetwork::getAllNodes(const std::string& startNode,
                                            int maxPathLength)
{
    // 找到从起始节点可达的所有节点
    std::unordered_set<std::string> reachableNodes;
    std::unordered_set<std::string> visited;
    dfsCollectReachableNodes(startNode, visited, reachableNodes, maxPathLength);

    // 将结果转换为vector并排序，以便输出一致
    std::vector<std::string> result(reachableNodes.begin(), reachableNodes.end());
    std::sort(result.begin(), result.end());

    return result;
}

std::vector<std::string> NodeNetwork::getCommonVariables(const std::string& node1,
                                                   const std::string& node2)
{
    if (commonVariables_.find(node1) == commonVariables_.end() || commonVariables_[node1].find(node2) ==
        commonVariables_[node1].end())
        return {}; // 如果没有公共变量点，返回空列表
    return commonVariables_[node1][node2];
}

std::unordered_map<std::string, std::vector<std::string>> NodeNetwork::getAllCommonVariables(const std::string& node)
{
    std::unordered_map<std::string, std::vector<std::string>> result;
    if (commonVariables_.find(node) == commonVariables_.end())
        return result; // 如果节点不存在，返回空映射

    for (const auto& [otherNode, commonVars] : commonVariables_[node])
    {
        result[otherNode] = commonVars;
    }

    return result;
}

std::vector<std::string> NodeNetwork::getNodeNeighbors(const std::string& node) const
{
    // 如果该节点不存在，返回空列表
    if (nodes_.find(node) == nodes_.end())
        return {};
    // 获取该节点的所有邻居节点
    std::vector<std::string> neighbors;
    for (const auto& var : nodes_.at(node))
    {
        for (const auto& neighbor : variableToNodes_.at(var))
        {
            if (neighbor != node)
            {
                if (std::find(neighbors.begin(), neighbors.end(), neighbor) == neighbors.end())
                    neighbors.push_back(neighbor);
            }
        }
    }
    return neighbors;
}

std::vector<std::string> NodeNetwork::getNodeNeighbors(const std::string& node,
                                                 const std::string& variable) const
{
    // 如果该节点不存在，返回空列表
    if (nodes_.find(node) == nodes_.end())
        return {};
    // 获取该节点的所有邻居节点
    std::vector<std::string> neighbors;
    for (const auto& neighbor : variableToNodes_.at(variable))
    {
        if (neighbor != node)
            neighbors.push_back(neighbor);
    }
    return neighbors;
}

std::vector<std::string> NodeNetwork::getNodesByVariable(const std::string& variable) const
{
    if (variableToNodes_.find(variable) == variableToNodes_.end())
        return {};
    std::vector<std::string> nodes;
    for (const auto& node : variableToNodes_.at(variable))
    {
        nodes.push_back(node);
    }
    return nodes;
}

void NodeNetwork::dfs(const std::string& currentNode,
                      std::unordered_set<std::string>& visited,
                      std::vector<std::string>& currentPath,
                      std::vector<std::vector<std::string>>& finalPaths,
                      size_t maxPathLength)
{
    visited.insert(currentNode);
    currentPath.push_back(currentNode);

    // 检查是否有未访问的邻居节点（不限于活跃变量）
    bool hasUnvisitedNeighbors = false;
    for (const auto& var : nodes_[currentNode])
    {
        for (const auto& neighbor : variableToNodes_[var])
        {
            if (neighbor != currentNode && visited.find(neighbor) == visited.end())
            {
                hasUnvisitedNeighbors = true;
                break;
            }
        }
        if (hasUnvisitedNeighbors)
            break;
    }

    // 如果没有未访问的邻居节点，或者路径长度达到限制，说明当前路径是一个最终路径
    if (!hasUnvisitedNeighbors || currentPath.size() >= maxPathLength)
    {
        finalPaths.push_back(currentPath);
    }
    else
    {
        for (const auto& var : nodes_[currentNode])
        {
            for (const auto& neighbor : variableToNodes_[var])
            {
                if (neighbor != currentNode && visited.find(neighbor) == visited.end())
                {
                    dfs(neighbor, visited, currentPath, finalPaths, maxPathLength);
                }
            }
        }
    }

    // 回溯前移除当前节点和标记
    currentPath.pop_back();
    visited.erase(currentNode);
}

void NodeNetwork::dfsCollectEndpoints(const std::string& currentNode,
                                      std::unordered_set<std::string>& visited,
                                      std::unordered_set<std::string>& result,
                                      size_t maxPathLength)
{
    visited.insert(currentNode);

    // 计算当前节点的所有邻居数量（不限于活跃变量）
    int neighborCount = 0;
    std::unordered_set<std::string> uniqueNeighbors;
    for (const auto& var : nodes_[currentNode])
    {
        for (const auto& neighbor : variableToNodes_[var])
        {
            if (neighbor != currentNode)
            {
                uniqueNeighbors.insert(neighbor);
            }
        }
    }
    neighborCount = uniqueNeighbors.size();

    // 如果当前节点邻居数 <= 1，或已达到最大路径长度，则视为最终端点
    if (neighborCount <= 1 || visited.size() >= maxPathLength)
    {
        result.insert(currentNode);
    }
    else
    {
        for (const auto& var : nodes_[currentNode])
        {
            for (const auto& neighbor : variableToNodes_[var])
            {
                if (neighbor != currentNode && visited.find(neighbor) == visited.end())
                {
                    dfsCollectEndpoints(neighbor, visited, result, maxPathLength);
                }
            }
        }
    }

    visited.erase(currentNode);
}

void NodeNetwork::dfsCollectReachableNodes(const std::string& currentNode,
                                           std::unordered_set<std::string>& visited,
                                           std::unordered_set<std::string>& reachableNodes,
                                           size_t maxPathLength)
{
    visited.insert(currentNode);
    reachableNodes.insert(currentNode);

    // 如果已达到最大路径长度，停止搜索
    if (visited.size() >= maxPathLength) {
        visited.erase(currentNode);
        return;
    }

    // 遍历所有变量和邻居节点（不限于活跃变量）
    if (nodes_.find(currentNode) != nodes_.end()) {
        for (const auto& var : nodes_.at(currentNode)) {
            if (variableToNodes_.find(var) != variableToNodes_.end()) {
                for (const auto& neighbor : variableToNodes_.at(var)) {
                    if (neighbor != currentNode && visited.find(neighbor) == visited.end()) {
                        dfsCollectReachableNodes(neighbor, visited, reachableNodes, maxPathLength);
                    }
                }
            }
        }
    }

    visited.erase(currentNode);
}

void NodeNetwork::updateCommonVariables(const std::string& node,
                                        const std::string& var)
{
    for (const auto& otherNode : variableToNodes_[var])
    {
        if (otherNode == node)
            continue; // 跳过自身

        // 更新 node 和 otherNode 之间的公共变量点
        commonVariables_[node][otherNode].push_back(var);
        commonVariables_[otherNode][node].push_back(var);
    }
}
