E:\cmake\bin\cmake.exe -E rm -f CMakeFiles\NodeNetwork.dir/objects.a
D:\msys64\mingw64\bin\ar.exe qc CMakeFiles\NodeNetwork.dir/objects.a @CMakeFiles\NodeNetwork.dir\objects1.rsp
D:\msys64\mingw64\bin\g++.exe -g -Wl,--whole-archive CMakeFiles\NodeNetwork.dir/objects.a -Wl,--no-whole-archive -o NodeNetwork.exe -Wl,--out-implib,libNodeNetwork.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\NodeNetwork.dir\linkLibs.rsp
